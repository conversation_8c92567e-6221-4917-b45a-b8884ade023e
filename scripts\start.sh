#!/bin/bash

# 量化投资平台一键启动脚本
# 支持 Linux、macOS、Windows (Git Bash)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 启动量化投资平台..."

# 进入项目根目录
cd "$(dirname "$0")/.."

# 创建日志目录
mkdir -p logs

# 检查依赖
log_info "检查运行环境..."

# 检查 Python
PYTHON_CMD=""
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    log_error "Python 未安装，请安装 Python 3.9+"
    log_info "下载地址: https://www.python.org/"
    exit 1
fi

# 检查 Node.js
if ! command -v node &> /dev/null; then
    log_error "Node.js 未安装，请安装 Node.js 16+"
    log_info "下载地址: https://nodejs.org/"
    exit 1
fi

# 检查版本
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    log_warning "Node.js 版本较低: $(node --version)，建议使用 16+ 版本"
fi

log_success "环境检查通过"

# 停止现有服务
log_info "停止现有服务..."
pkill -f "vite\|npm.*dev" 2>/dev/null || true
pkill -f "uvicorn\|fastapi\|main_minimal\|main_simple" 2>/dev/null || true
sleep 2

# 启动后端
log_info "启动后端服务..."
cd backend

# 选择Python启动文件
BACKEND_FILE=""
if [ -f "app/main_minimal.py" ]; then
    BACKEND_FILE="app/main_minimal.py"
    log_info "使用最小化后端: $BACKEND_FILE"
elif [ -f "app/main_simple.py" ]; then
    BACKEND_FILE="app/main_simple.py"
    log_info "使用简化后端: $BACKEND_FILE"
elif [ -f "app/main.py" ]; then
    BACKEND_FILE="app/main.py"
    log_info "使用完整后端: $BACKEND_FILE"
else
    log_error "未找到后端启动文件"
    exit 1
fi

# 启动后端服务
nohup $PYTHON_CMD $BACKEND_FILE > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
log_info "后端 PID: $BACKEND_PID"

# 等待后端启动
log_info "等待后端服务启动..."
for i in {1..15}; do
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "后端服务启动成功"
        break
    elif [ $i -eq 15 ]; then
        log_error "后端服务启动失败"
        log_error "后端日志:"
        tail -20 ../logs/backend.log
        exit 1
    else
        echo -n "."
        sleep 2
    fi
done

# 返回项目根目录
cd ..

# 启动前端
log_info "启动前端服务..."
cd frontend

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    log_info "安装前端依赖..."
    npm install
    if [ $? -ne 0 ]; then
        log_error "前端依赖安装失败"
        kill $BACKEND_PID 2>/dev/null
        exit 1
    fi
else
    log_info "前端依赖已存在"
fi

# 启动前端开发服务器
nohup npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
log_info "前端 PID: $FRONTEND_PID"

# 等待前端启动
log_info "等待前端服务启动..."
for i in {1..20}; do
    if curl -f http://localhost:5173 > /dev/null 2>&1; then
        log_success "前端服务启动成功"
        break
    elif [ $i -eq 20 ]; then
        log_error "前端服务启动失败"
        log_error "前端日志:"
        tail -20 ../logs/frontend.log
        kill $BACKEND_PID 2>/dev/null
        exit 1
    else
        echo -n "."
        sleep 3
    fi
done

# 返回项目根目录
cd ..

# 保存进程ID到文件
echo $BACKEND_PID > .backend.pid
echo $FRONTEND_PID > .frontend.pid

echo ""
log_success "🎉 量化投资平台启动完成！"
echo ""
echo "📊 服务地址:"
echo "   前端应用: http://localhost:5173"
echo "   后端API: http://localhost:8000"
echo "   API文档: http://localhost:8000/docs"
echo ""
echo "📋 进程信息:"
echo "   后端 PID: $BACKEND_PID"
echo "   前端 PID: $FRONTEND_PID"
echo ""
echo "📄 日志文件:"
echo "   后端日志: logs/backend.log"
echo "   前端日志: logs/frontend.log"
echo ""
echo "🔧 管理命令:"
echo "   停止服务: ./scripts/stop.sh"
echo "   查看状态: ./scripts/status.sh"
echo "   重启服务: ./scripts/restart.sh"
echo ""
log_info "提示: 首次启动可能需要较长时间，请耐心等待"
log_info "如遇问题，请查看日志文件或运行 ./scripts/status.sh"